<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'student_id' => null,
        ]);

        // Create sample books
        $books = [
            [
                'title' => 'The Great Gatsby',
                'author' => '<PERSON><PERSON> Scott <PERSON>',
                'isbn' => '9780743273565',
                'description' => 'A classic American novel set in the Jazz Age.',
                'category' => 'Fiction',
                'total_copies' => 5,
                'available_copies' => 5,
                'publisher' => 'Scribner',
                'publication_date' => '1925-04-10',
            ],
            [
                'title' => 'To Kill a Mockingbird',
                'author' => '<PERSON> Lee',
                'isbn' => '9780061120084',
                'description' => 'A gripping tale of racial injustice and childhood innocence.',
                'category' => 'Fiction',
                'total_copies' => 3,
                'available_copies' => 3,
                'publisher' => 'J.B. Lippincott & Co.',
                'publication_date' => '1960-07-11',
            ],
            [
                'title' => 'Introduction to Algorithms',
                'author' => 'Thomas H. Cormen',
                'isbn' => '9780262033848',
                'description' => 'Comprehensive introduction to algorithms and data structures.',
                'category' => 'Computer Science',
                'total_copies' => 10,
                'available_copies' => 10,
                'publisher' => 'MIT Press',
                'publication_date' => '2009-07-31',
            ],
            [
                'title' => 'Clean Code',
                'author' => 'Robert C. Martin',
                'isbn' => '9780132350884',
                'description' => 'A handbook of agile software craftsmanship.',
                'category' => 'Programming',
                'total_copies' => 7,
                'available_copies' => 7,
                'publisher' => 'Prentice Hall',
                'publication_date' => '2008-08-01',
            ],
            [
                'title' => 'The Art of War',
                'author' => 'Sun Tzu',
                'isbn' => '9781599869773',
                'description' => 'Ancient Chinese military treatise.',
                'category' => 'Philosophy',
                'total_copies' => 4,
                'available_copies' => 4,
                'publisher' => 'Filiquarian Publishing',
                'publication_date' => '2006-01-01',
            ],
        ];

        foreach ($books as $book) {
            Book::create($book);
        }
    }
}
