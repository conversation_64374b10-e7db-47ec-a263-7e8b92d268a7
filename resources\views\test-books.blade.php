<!DOCTYPE html>
<html>
<head>
    <title>Test Books</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .book { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .search-form { margin-bottom: 20px; padding: 15px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>Test Books Page</h1>
    
    <!-- Search Form -->
    <div class="search-form">
        <form method="GET">
            <input type="text" name="search" placeholder="Search books..." value="{{ request('search') }}" style="padding: 8px; width: 300px;">
            <button type="submit" style="padding: 8px 15px;">Search</button>
            <a href="{{ url('/test-books') }}" style="padding: 8px 15px; background: #ccc; text-decoration: none; color: black;">Clear</a>
        </form>
    </div>

    <!-- Books Display -->
    @php
        $query = \App\Models\Book::query();
        
        if(request('search')) {
            $search = request('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('author', 'like', "%{$search}%")
                  ->orWhere('isbn', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }
        
        $books = $query->get();
    @endphp

    <p><strong>Total Books Found: {{ $books->count() }}</strong></p>
    
    @if(request('search'))
        <p><em>Search term: "{{ request('search') }}"</em></p>
    @endif

    @forelse($books as $book)
        <div class="book">
            <h3>{{ $book->title }}</h3>
            <p><strong>Author:</strong> {{ $book->author }}</p>
            <p><strong>Category:</strong> {{ $book->category ?? 'N/A' }}</p>
            <p><strong>ISBN:</strong> {{ $book->isbn }}</p>
            <p><strong>Available:</strong> {{ $book->available_copies }}/{{ $book->total_copies }}</p>
            @if($book->description)
                <p><strong>Description:</strong> {{ $book->description }}</p>
            @endif
        </div>
    @empty
        <p style="color: red;">No books found!</p>
    @endforelse

    <hr>
    <h2>Debug Info:</h2>
    <p><strong>Request Search:</strong> {{ request('search') ?? 'None' }}</p>
    <p><strong>All Books Count:</strong> {{ \App\Models\Book::count() }}</p>
    
    <h3>All Books in Database:</h3>
    @foreach(\App\Models\Book::all() as $book)
        <li>{{ $book->title }} by {{ $book->author }}</li>
    @endforeach
</body>
</html>
