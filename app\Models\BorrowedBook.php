<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BorrowedBook extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'book_id',
        'borrowed_date',
        'return_date',
        'actual_return_date',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'borrowed_date' => 'date',
            'return_date' => 'date',
            'actual_return_date' => 'date',
        ];
    }

    /**
     * Get the user who borrowed the book
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book that was borrowed
     */
    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Check if the book is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'borrowed' && $this->return_date < now()->toDateString();
    }

    /**
     * Mark book as returned
     */
    public function markAsReturned()
    {
        $this->update([
            'status' => 'returned',
            'actual_return_date' => now()->toDateString(),
        ]);

        // Increase available copies
        $this->book->returnBook();
    }
}
