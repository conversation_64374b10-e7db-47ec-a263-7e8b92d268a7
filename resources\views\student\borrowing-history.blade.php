<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('My Borrowing History') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Back to Dashboard -->
            <div class="mb-6">
                <a href="{{ route('student.dashboard') }}" class="text-blue-600 hover:text-blue-800">
                    ← Back to Dashboard
                </a>
            </div>

            <!-- Borrowing History -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-6">Complete Borrowing History</h3>
                    
                    @if($borrowingHistory->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book Details</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrowed Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Borrowed</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($borrowingHistory as $borrowedBook)
                                        <tr class="{{ $borrowedBook->isOverdue() && $borrowedBook->status === 'borrowed' ? 'bg-red-50' : '' }}">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $borrowedBook->book->title }}</div>
                                                <div class="text-sm text-gray-500">by {{ $borrowedBook->book->author }}</div>
                                                <div class="text-xs text-gray-400">ISBN: {{ $borrowedBook->book->isbn }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $borrowedBook->borrowed_date->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $borrowedBook->return_date->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($borrowedBook->actual_return_date)
                                                    {{ $borrowedBook->actual_return_date->format('M d, Y') }}
                                                @else
                                                    <span class="text-gray-400">Not returned</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($borrowedBook->status === 'returned')
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                        Returned
                                                    </span>
                                                @elseif($borrowedBook->isOverdue())
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                        Overdue
                                                    </span>
                                                @else
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                        Active
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($borrowedBook->status === 'returned')
                                                    {{ $borrowedBook->borrowed_date->diffInDays($borrowedBook->actual_return_date) }} days
                                                @else
                                                    {{ $borrowedBook->borrowed_date->diffInDays(now()) }} days (ongoing)
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($borrowingHistory->hasPages())
                            <div class="mt-6">
                                {{ $borrowingHistory->links() }}
                            </div>
                        @endif

                        <!-- Summary Statistics -->
                        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-blue-800 text-sm font-medium">Total Books Borrowed</div>
                                <div class="text-blue-900 text-2xl font-bold">{{ $borrowingHistory->total() }}</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-green-800 text-sm font-medium">Books Returned</div>
                                <div class="text-green-900 text-2xl font-bold">{{ $borrowingHistory->where('status', 'returned')->count() }}</div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="text-yellow-800 text-sm font-medium">Currently Borrowed</div>
                                <div class="text-yellow-900 text-2xl font-bold">{{ $borrowingHistory->where('status', 'borrowed')->count() }}</div>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="text-red-800 text-sm font-medium">Overdue Books</div>
                                <div class="text-red-900 text-2xl font-bold">
                                    {{ $borrowingHistory->filter(function($book) { return $book->isOverdue() && $book->status === 'borrowed'; })->count() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="text-gray-400 mb-4">
                                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No borrowing history yet</h3>
                            <p class="text-gray-500 mb-6">You haven't borrowed any books yet. Start exploring our collection!</p>
                            <a href="{{ route('books.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Browse Books
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
