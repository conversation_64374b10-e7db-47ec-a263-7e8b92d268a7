<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Browse Books')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('books.index')); ?>" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Search -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700">Search Books</label>
                                <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                                       placeholder="Search by title, author, ISBN..." 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>

                            <!-- Category Filter -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                                <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Categories</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>>
                                            <?php echo e($category); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <!-- Availability Filter -->
                            <div>
                                <label for="available" class="block text-sm font-medium text-gray-700">Availability</label>
                                <select name="available" id="available" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Books</option>
                                    <option value="1" <?php echo e(request('available') == '1' ? 'selected' : ''); ?>>Available Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-2">
                            <a href="<?php echo e(route('books.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Clear Filters
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Books Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php $__empty_1 = true; $__currentLoopData = $books; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $book): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($book->title); ?></h3>
                                <p class="text-sm text-gray-600 mb-1">by <?php echo e($book->author); ?></p>
                                <?php if($book->category): ?>
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                        <?php echo e($book->category); ?>

                                    </span>
                                <?php endif; ?>
                            </div>

                            <?php if($book->description): ?>
                                <p class="text-sm text-gray-700 mb-4 line-clamp-3"><?php echo e(Str::limit($book->description, 100)); ?></p>
                            <?php endif; ?>

                            <div class="mb-4">
                                <div class="flex justify-between items-center text-sm text-gray-600">
                                    <span>Available: <?php echo e($book->available_copies); ?>/<?php echo e($book->total_copies); ?></span>
                                    <span class="text-xs">ISBN: <?php echo e($book->isbn); ?></span>
                                </div>
                                
                                <?php if($book->publisher): ?>
                                    <p class="text-xs text-gray-500 mt-1"><?php echo e($book->publisher); ?></p>
                                <?php endif; ?>
                                
                                <?php if($book->publication_date): ?>
                                    <p class="text-xs text-gray-500">Published: <?php echo e($book->publication_date->format('Y')); ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('books.show', $book)); ?>" class="flex-1 bg-gray-500 hover:bg-gray-700 text-white text-center font-bold py-2 px-4 rounded text-sm">
                                    View Details
                                </a>
                                
                                <?php if($book->isAvailable()): ?>
                                    <form action="<?php echo e(route('books.borrow', $book)); ?>" method="POST" class="flex-1">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"
                                                onclick="return confirm('Are you sure you want to borrow this book?')">
                                            Borrow
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <button disabled class="flex-1 bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded text-sm cursor-not-allowed">
                                        Not Available
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-span-full">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6 text-center">
                                <p class="text-gray-500">No books found matching your criteria.</p>
                                <?php if(request()->hasAny(['search', 'category', 'available'])): ?>
                                    <a href="<?php echo e(route('books.index')); ?>" class="text-blue-600 hover:text-blue-800 mt-2 inline-block">
                                        Clear filters to see all books
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if($books->hasPages()): ?>
                <div class="mt-8">
                    <?php echo e($books->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Xampp\htdocs\iti_projec\resources\views/books/index.blade.php ENDPATH**/ ?>