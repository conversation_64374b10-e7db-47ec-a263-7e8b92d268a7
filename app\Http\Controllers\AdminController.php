<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BorrowedBook;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class AdminController extends Controller
{
    /**
     * Display admin dashboard
     */
    public function dashboard()
    {
        $totalBooks = Book::count();
        $totalUsers = User::where('role', 'student')->count();
        $borrowedBooks = BorrowedBook::where('status', 'borrowed')->count();
        $overdueBooks = BorrowedBook::where('status', 'borrowed')
            ->where('return_date', '<', now()->toDateString())
            ->count();

        $recentBorrowedBooks = BorrowedBook::with(['user', 'book'])
            ->where('status', 'borrowed')
            ->latest()
            ->take(10)
            ->get();

        return view('admin.dashboard', compact(
            'totalBooks',
            'totalUsers',
            'borrowedBooks',
            'overdueBooks',
            'recentBorrowedBooks'
        ));
    }

    /**
     * Display all books
     */
    public function books()
    {
        $books = Book::paginate(15);
        return view('admin.books.index', compact('books'));
    }

    /**
     * Show form to create new book
     */
    public function createBook()
    {
        return view('admin.books.create');
    }

    /**
     * Store new book
     */
    public function storeBook(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'isbn' => 'required|string|unique:books,isbn',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'nullable|string|max:255',
            'total_copies' => 'required|integer|min:1',
            'publisher' => 'nullable|string|max:255',
            'publication_date' => 'nullable|date',
        ]);

        $imageName = null;
        if ($request->hasFile('image')) {
            $imageName = time() . '.' . $request->image->extension();
            $request->image->move(public_path('storage/books'), $imageName);
        }

        $book = Book::create([
            'title' => $request->title,
            'author' => $request->author,
            'isbn' => $request->isbn,
            'description' => $request->description,
            'image' => $imageName,
            'category' => $request->category,
            'total_copies' => $request->total_copies,
            'available_copies' => $request->total_copies,
            'publisher' => $request->publisher,
            'publication_date' => $request->publication_date,
        ]);

        return redirect()->route('admin.books')->with('success', 'Book created successfully!');
    }

    /**
     * Show form to edit book
     */
    public function editBook(Book $book)
    {
        return view('admin.books.edit', compact('book'));
    }

    /**
     * Update book
     */
    public function updateBook(Request $request, Book $book)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'isbn' => ['required', 'string', Rule::unique('books')->ignore($book->id)],
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:255',
            'total_copies' => 'required|integer|min:1',
            'publisher' => 'nullable|string|max:255',
            'publication_date' => 'nullable|date',
        ]);

        // Calculate new available copies based on the difference
        $copyDifference = $request->total_copies - $book->total_copies;
        $newAvailableCopies = $book->available_copies + $copyDifference;

        // Ensure available copies don't go below 0
        $newAvailableCopies = max(0, $newAvailableCopies);

        $book->update([
            'title' => $request->title,
            'author' => $request->author,
            'isbn' => $request->isbn,
            'description' => $request->description,
            'category' => $request->category,
            'total_copies' => $request->total_copies,
            'available_copies' => $newAvailableCopies,
            'publisher' => $request->publisher,
            'publication_date' => $request->publication_date,
        ]);

        return redirect()->route('admin.books')->with('success', 'Book updated successfully!');
    }

    /**
     * Delete book
     */
    public function deleteBook(Book $book)
    {
        // Check if book has active borrowings
        $activeBorrowings = BorrowedBook::where('book_id', $book->id)
            ->where('status', 'borrowed')
            ->count();

        if ($activeBorrowings > 0) {
            return redirect()->route('admin.books')
                ->with('error', 'Cannot delete book with active borrowings.');
        }

        $book->delete();
        return redirect()->route('admin.books')->with('success', 'Book deleted successfully!');
    }

    /**
     * Display all users
     */
    public function users()
    {
        $users = User::where('role', 'student')->paginate(15);
        return view('admin.users.index', compact('users'));
    }

    /**
     * Search user by student ID
     */
    public function searchUser(Request $request)
    {
        $request->validate([
            'student_id' => 'required|string',
        ]);

        $user = User::where('student_id', $request->student_id)
            ->where('role', 'student')
            ->first();

        if (!$user) {
            return redirect()->route('admin.users')
                ->with('error', 'Student not found with ID: ' . $request->student_id);
        }

        $borrowedBooks = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->latest()
            ->paginate(10);

        return view('admin.users.show', compact('user', 'borrowedBooks'));
    }

    /**
     * Show user details
     */
    public function showUser(User $user)
    {
        $borrowedBooks = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->latest()
            ->paginate(10);

        return view('admin.users.show', compact('user', 'borrowedBooks'));
    }

    /**
     * Show admin profile
     */
    public function profile()
    {
        return view('admin.profile');
    }

    /**
     * Update admin profile
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Check current password if new password is provided
        if ($request->filled('password')) {
            if (!$request->filled('current_password') || !Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return redirect()->route('admin.profile')->with('success', 'Profile updated successfully!');
    }
}
