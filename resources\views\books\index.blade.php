<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Browse Books') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <form method="GET" action="{{ route('books.index') }}" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Search -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700">Search Books</label>
                                <input type="text" name="search" id="search" value="{{ request('search') }}"
                                       placeholder="Search by title, author, ISBN..."
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>

                            <!-- Category Filter -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                                <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                            {{ $category }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Availability Filter -->
                            <div>
                                <label for="available" class="block text-sm font-medium text-gray-700">Availability</label>
                                <select name="available" id="available" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Books</option>
                                    <option value="1" {{ request('available') == '1' ? 'selected' : '' }}>Available Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-2">
                            <a href="{{ route('books.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Clear Filters
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Books Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @forelse($books as $book)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-lg transition-shadow duration-300">
                        <!-- Book Cover Thumbnail -->
                        @php
                            $colorIndex = abs(crc32($book->title)) % 6;
                            $gradientStyle = match($colorIndex) {
                                0 => 'background: linear-gradient(135deg, #3b82f6, #1d4ed8);',
                                1 => 'background: linear-gradient(135deg, #10b981, #047857);',
                                2 => 'background: linear-gradient(135deg, #8b5cf6, #5b21b6);',
                                3 => 'background: linear-gradient(135deg, #ef4444, #b91c1c);',
                                4 => 'background: linear-gradient(135deg, #6366f1, #3730a3);',
                                5 => 'background: linear-gradient(135deg, #ec4899, #be185d);',
                                default => 'background: linear-gradient(135deg, #6b7280, #374151);'
                            };
                        @endphp
                        <div class="h-48 flex items-center justify-center relative overflow-hidden rounded-t-lg" style="{{ $gradientStyle }}">
                            <!-- Book Icon -->
                            <div class="text-center text-white z-10">
                                <svg class="mx-auto h-12 w-12 mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <p class="text-xs font-medium">{{ Str::limit($book->title, 20) }}</p>
                            </div>

                            <!-- Decorative Elements -->
                            <div class="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-20 rounded-full"></div>
                            <div class="absolute bottom-2 left-2 w-4 h-4 bg-white bg-opacity-10 rounded-full"></div>
                        </div>

                        <div class="p-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $book->title }}</h3>
                                <p class="text-sm text-gray-600 mb-1">by {{ $book->author }}</p>
                                @if($book->category)
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                        {{ $book->category }}
                                    </span>
                                @endif
                            </div>

                            @if($book->description)
                                <p class="text-sm text-gray-700 mb-4 line-clamp-3">{{ Str::limit($book->description, 100) }}</p>
                            @endif

                            <div class="mb-4">
                                <div class="flex justify-between items-center text-sm text-gray-600">
                                    <span>Available: {{ $book->available_copies }}/{{ $book->total_copies }}</span>
                                    <span class="text-xs">ISBN: {{ $book->isbn }}</span>
                                </div>

                                @if($book->publisher)
                                    <p class="text-xs text-gray-500 mt-1">{{ $book->publisher }}</p>
                                @endif

                                @if($book->publication_date)
                                    <p class="text-xs text-gray-500">Published: {{ $book->publication_date->format('Y') }}</p>
                                @endif
                            </div>

                            <div class="flex space-x-2">
                                <a href="{{ route('books.show', $book) }}" class="flex-1 bg-gray-500 hover:bg-gray-700 text-white text-center font-bold py-2 px-4 rounded text-sm">
                                    View Details
                                </a>

                                @if($book->isAvailable())
                                    <form action="{{ route('books.borrow', $book) }}" method="POST" class="flex-1">
                                        @csrf
                                        <button type="submit" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"
                                                onclick="return confirm('Are you sure you want to borrow this book?')">
                                            Borrow
                                        </button>
                                    </form>
                                @else
                                    <button disabled class="flex-1 bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded text-sm cursor-not-allowed">
                                        Not Available
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6 text-center">
                                <p class="text-gray-500">No books found matching your criteria.</p>
                                @if(request()->hasAny(['search', 'category', 'available']))
                                    <a href="{{ route('books.index') }}" class="text-blue-600 hover:text-blue-800 mt-2 inline-block">
                                        Clear filters to see all books
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($books->hasPages())
                <div class="mt-8">
                    {{ $books->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
