<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e($book->title); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="<?php echo e(route('books.index')); ?>" class="text-blue-600 hover:text-blue-800">
                    ← Back to Books
                </a>
            </div>

            <!-- Book Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Book Cover -->
                        <?php
                            $colors = [
                                'blue' => ['from-blue-500', 'to-blue-700'],
                                'green' => ['from-green-500', 'to-green-700'],
                                'purple' => ['from-purple-500', 'to-purple-700'],
                                'red' => ['from-red-500', 'to-red-700'],
                                'indigo' => ['from-indigo-500', 'to-indigo-700'],
                                'pink' => ['from-pink-500', 'to-pink-700']
                            ];
                            $colorKeys = array_keys($colors);
                            $colorIndex = abs(crc32($book->title)) % count($colorKeys);
                            $selectedColor = $colors[$colorKeys[$colorIndex]];
                        ?>
                        <div class="lg:col-span-1">
                            <div class="bg-gradient-to-br <?php echo e($selectedColor[0]); ?> <?php echo e($selectedColor[1]); ?> rounded-lg h-96 flex items-center justify-center shadow-lg relative overflow-hidden">
                                <!-- Background Pattern -->
                                <div class="absolute inset-0 opacity-10">
                                    <div class="w-full h-full" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px); background-size: 40px 40px;"></div>
                                </div>

                                <!-- Book Icon and Title -->
                                <div class="text-center text-white z-10 p-6">
                                    <svg class="mx-auto h-20 w-20 mb-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <h3 class="text-lg font-bold mb-2 leading-tight"><?php echo e(Str::limit($book->title, 30)); ?></h3>
                                    <p class="text-sm opacity-90"><?php echo e($book->author); ?></p>
                                    <?php if($book->category): ?>
                                        <div class="mt-3">
                                            <span class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                                                <?php echo e($book->category); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Decorative Elements -->
                                <div class="absolute top-4 right-4 w-12 h-12 bg-white bg-opacity-10 rounded-full"></div>
                                <div class="absolute bottom-4 left-4 w-8 h-8 bg-white bg-opacity-10 rounded-full"></div>
                                <div class="absolute top-1/2 left-2 w-2 h-16 bg-white bg-opacity-20 rounded-full transform -translate-y-1/2"></div>
                            </div>
                        </div>

                        <!-- Book Information -->
                        <div class="lg:col-span-2">
                            <div class="space-y-6">
                                <!-- Title and Author -->
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900 mb-2"><?php echo e($book->title); ?></h1>
                                    <p class="text-xl text-gray-600 mb-4">by <?php echo e($book->author); ?></p>

                                    <?php if($book->category): ?>
                                        <span class="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                                            <?php echo e($book->category); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Description -->
                                <?php if($book->description): ?>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                                        <p class="text-gray-700 leading-relaxed"><?php echo e($book->description); ?></p>
                                    </div>
                                <?php endif; ?>

                                <!-- Book Details -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Book Details</h3>
                                    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">ISBN</dt>
                                            <dd class="mt-1 text-sm text-gray-900"><?php echo e($book->isbn); ?></dd>
                                        </div>

                                        <?php if($book->publisher): ?>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Publisher</dt>
                                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($book->publisher); ?></dd>
                                            </div>
                                        <?php endif; ?>

                                        <?php if($book->publication_date): ?>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Publication Date</dt>
                                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($book->publication_date->format('F d, Y')); ?></dd>
                                            </div>
                                        <?php endif; ?>

                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Total Copies</dt>
                                            <dd class="mt-1 text-sm text-gray-900"><?php echo e($book->total_copies); ?></dd>
                                        </div>

                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Available Copies</dt>
                                            <dd class="mt-1 text-sm text-gray-900">
                                                <span class="font-semibold <?php echo e($book->available_copies > 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                                    <?php echo e($book->available_copies); ?>

                                                </span>
                                            </dd>
                                        </div>

                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                                            <dd class="mt-1">
                                                <?php if($book->isAvailable()): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Available
                                                    </span>
                                                <?php else: ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Not Available
                                                    </span>
                                                <?php endif; ?>
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Action Buttons -->
                                <?php if(auth()->guard()->check()): ?>
                                    <?php if(auth()->user()->isStudent()): ?>
                                        <div class="flex space-x-4">
                                            <?php if($book->isAvailable()): ?>
                                                <?php
                                                    $alreadyBorrowed = auth()->user()->borrowedBooks()
                                                        ->where('book_id', $book->id)
                                                        ->where('status', 'borrowed')
                                                        ->exists();
                                                ?>

                                                <?php if($alreadyBorrowed): ?>
                                                    <button disabled class="bg-gray-300 text-gray-500 font-bold py-3 px-6 rounded cursor-not-allowed">
                                                        Already Borrowed
                                                    </button>
                                                <?php else: ?>
                                                    <form action="<?php echo e(route('books.borrow', $book)); ?>" method="POST" class="inline">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded"
                                                                onclick="return confirm('Are you sure you want to borrow this book? You will have 14 days to return it.')">
                                                            Borrow This Book
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <button disabled class="bg-gray-300 text-gray-500 font-bold py-3 px-6 rounded cursor-not-allowed">
                                                    Not Available
                                                </button>
                                            <?php endif; ?>

                                            <a href="<?php echo e(route('books.index')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                                                Browse More Books
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-yellow-800">Login Required</h3>
                                                <div class="mt-2 text-sm text-yellow-700">
                                                    <p>You need to login as a student to borrow books.</p>
                                                </div>
                                                <div class="mt-4">
                                                    <div class="-mx-2 -my-1.5 flex">
                                                        <a href="<?php echo e(route('login')); ?>" class="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100">
                                                            Login
                                                        </a>
                                                        <a href="<?php echo e(route('register')); ?>" class="ml-3 bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100">
                                                            Register
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Books or Additional Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">More Books by <?php echo e($book->author); ?></h3>

                    <?php
                        $relatedBooks = \App\Models\Book::where('author', $book->author)
                            ->where('id', '!=', $book->id)
                            ->limit(3)
                            ->get();
                    ?>

                    <?php if($relatedBooks->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <?php $__currentLoopData = $relatedBooks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedBook): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <h4 class="font-semibold text-gray-900 mb-2"><?php echo e($relatedBook->title); ?></h4>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo e($relatedBook->category); ?></p>
                                    <p class="text-xs text-gray-500 mb-3">Available: <?php echo e($relatedBook->available_copies); ?>/<?php echo e($relatedBook->total_copies); ?></p>
                                    <a href="<?php echo e(route('books.show', $relatedBook)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details →
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500">No other books by this author are currently available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Xampp\htdocs\iti_projec\resources\views/books/show.blade.php ENDPATH**/ ?>