<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $book->title }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="{{ route('books.index') }}" class="text-blue-600 hover:text-blue-800">
                    ← Back to Books
                </a>
            </div>

            <!-- Book Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Book Cover Placeholder -->
                        <div class="lg:col-span-1">
                            <div class="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
                                <div class="text-center text-gray-500">
                                    <svg class="mx-auto h-16 w-16 mb-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <p class="text-sm">Book Cover</p>
                                    <p class="text-xs">{{ $book->title }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Book Information -->
                        <div class="lg:col-span-2">
                            <div class="space-y-6">
                                <!-- Title and Author -->
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $book->title }}</h1>
                                    <p class="text-xl text-gray-600 mb-4">by {{ $book->author }}</p>
                                    
                                    @if($book->category)
                                        <span class="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                                            {{ $book->category }}
                                        </span>
                                    @endif
                                </div>

                                <!-- Description -->
                                @if($book->description)
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                                        <p class="text-gray-700 leading-relaxed">{{ $book->description }}</p>
                                    </div>
                                @endif

                                <!-- Book Details -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Book Details</h3>
                                    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">ISBN</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $book->isbn }}</dd>
                                        </div>
                                        
                                        @if($book->publisher)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Publisher</dt>
                                                <dd class="mt-1 text-sm text-gray-900">{{ $book->publisher }}</dd>
                                            </div>
                                        @endif
                                        
                                        @if($book->publication_date)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Publication Date</dt>
                                                <dd class="mt-1 text-sm text-gray-900">{{ $book->publication_date->format('F d, Y') }}</dd>
                                            </div>
                                        @endif
                                        
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Total Copies</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $book->total_copies }}</dd>
                                        </div>
                                        
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Available Copies</dt>
                                            <dd class="mt-1 text-sm text-gray-900">
                                                <span class="font-semibold {{ $book->available_copies > 0 ? 'text-green-600' : 'text-red-600' }}">
                                                    {{ $book->available_copies }}
                                                </span>
                                            </dd>
                                        </div>
                                        
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                                            <dd class="mt-1">
                                                @if($book->isAvailable())
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Available
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Not Available
                                                    </span>
                                                @endif
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Action Buttons -->
                                @auth
                                    @if(auth()->user()->isStudent())
                                        <div class="flex space-x-4">
                                            @if($book->isAvailable())
                                                @php
                                                    $alreadyBorrowed = auth()->user()->borrowedBooks()
                                                        ->where('book_id', $book->id)
                                                        ->where('status', 'borrowed')
                                                        ->exists();
                                                @endphp
                                                
                                                @if($alreadyBorrowed)
                                                    <button disabled class="bg-gray-300 text-gray-500 font-bold py-3 px-6 rounded cursor-not-allowed">
                                                        Already Borrowed
                                                    </button>
                                                @else
                                                    <form action="{{ route('books.borrow', $book) }}" method="POST" class="inline">
                                                        @csrf
                                                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded"
                                                                onclick="return confirm('Are you sure you want to borrow this book? You will have 14 days to return it.')">
                                                            Borrow This Book
                                                        </button>
                                                    </form>
                                                @endif
                                            @else
                                                <button disabled class="bg-gray-300 text-gray-500 font-bold py-3 px-6 rounded cursor-not-allowed">
                                                    Not Available
                                                </button>
                                            @endif
                                            
                                            <a href="{{ route('books.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                                                Browse More Books
                                            </a>
                                        </div>
                                    @endif
                                @else
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-yellow-800">Login Required</h3>
                                                <div class="mt-2 text-sm text-yellow-700">
                                                    <p>You need to login as a student to borrow books.</p>
                                                </div>
                                                <div class="mt-4">
                                                    <div class="-mx-2 -my-1.5 flex">
                                                        <a href="{{ route('login') }}" class="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100">
                                                            Login
                                                        </a>
                                                        <a href="{{ route('register') }}" class="ml-3 bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100">
                                                            Register
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Books or Additional Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">More Books by {{ $book->author }}</h3>
                    
                    @php
                        $relatedBooks = \App\Models\Book::where('author', $book->author)
                            ->where('id', '!=', $book->id)
                            ->limit(3)
                            ->get();
                    @endphp
                    
                    @if($relatedBooks->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            @foreach($relatedBooks as $relatedBook)
                                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <h4 class="font-semibold text-gray-900 mb-2">{{ $relatedBook->title }}</h4>
                                    <p class="text-sm text-gray-600 mb-2">{{ $relatedBook->category }}</p>
                                    <p class="text-xs text-gray-500 mb-3">Available: {{ $relatedBook->available_copies }}/{{ $relatedBook->total_copies }}</p>
                                    <a href="{{ route('books.show', $relatedBook) }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details →
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500">No other books by this author are currently available.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
