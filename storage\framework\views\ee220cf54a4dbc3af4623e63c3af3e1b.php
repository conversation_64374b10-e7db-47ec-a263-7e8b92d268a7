<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('My Borrowing History')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Back to Dashboard -->
            <div class="mb-6">
                <a href="<?php echo e(route('student.dashboard')); ?>" class="text-blue-600 hover:text-blue-800">
                    ← Back to Dashboard
                </a>
            </div>

            <!-- Borrowing History -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-6">Complete Borrowing History</h3>
                    
                    <?php if($borrowingHistory->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book Details</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrowed Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Borrowed</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $borrowingHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $borrowedBook): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($borrowedBook->isOverdue() && $borrowedBook->status === 'borrowed' ? 'bg-red-50' : ''); ?>">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?php echo e($borrowedBook->book->title); ?></div>
                                                <div class="text-sm text-gray-500">by <?php echo e($borrowedBook->book->author); ?></div>
                                                <div class="text-xs text-gray-400">ISBN: <?php echo e($borrowedBook->book->isbn); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($borrowedBook->borrowed_date->format('M d, Y')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($borrowedBook->return_date->format('M d, Y')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php if($borrowedBook->actual_return_date): ?>
                                                    <?php echo e($borrowedBook->actual_return_date->format('M d, Y')); ?>

                                                <?php else: ?>
                                                    <span class="text-gray-400">Not returned</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if($borrowedBook->status === 'returned'): ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                        Returned
                                                    </span>
                                                <?php elseif($borrowedBook->isOverdue()): ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                        Overdue
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                        Active
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php if($borrowedBook->status === 'returned'): ?>
                                                    <?php echo e($borrowedBook->borrowed_date->diffInDays($borrowedBook->actual_return_date)); ?> days
                                                <?php else: ?>
                                                    <?php echo e($borrowedBook->borrowed_date->diffInDays(now())); ?> days (ongoing)
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if($borrowingHistory->hasPages()): ?>
                            <div class="mt-6">
                                <?php echo e($borrowingHistory->links()); ?>

                            </div>
                        <?php endif; ?>

                        <!-- Summary Statistics -->
                        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-blue-800 text-sm font-medium">Total Books Borrowed</div>
                                <div class="text-blue-900 text-2xl font-bold"><?php echo e($borrowingHistory->total()); ?></div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-green-800 text-sm font-medium">Books Returned</div>
                                <div class="text-green-900 text-2xl font-bold"><?php echo e($borrowingHistory->where('status', 'returned')->count()); ?></div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="text-yellow-800 text-sm font-medium">Currently Borrowed</div>
                                <div class="text-yellow-900 text-2xl font-bold"><?php echo e($borrowingHistory->where('status', 'borrowed')->count()); ?></div>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="text-red-800 text-sm font-medium">Overdue Books</div>
                                <div class="text-red-900 text-2xl font-bold">
                                    <?php echo e($borrowingHistory->filter(function($book) { return $book->isOverdue() && $book->status === 'borrowed'; })->count()); ?>

                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <div class="text-gray-400 mb-4">
                                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No borrowing history yet</h3>
                            <p class="text-gray-500 mb-6">You haven't borrowed any books yet. Start exploring our collection!</p>
                            <a href="<?php echo e(route('books.index')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Browse Books
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Xampp\htdocs\iti_projec\resources\views/student/borrowing-history.blade.php ENDPATH**/ ?>