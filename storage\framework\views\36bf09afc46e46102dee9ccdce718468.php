<!DOCTYPE html>
<html>
<head>
    <title>Test Books</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .book { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .search-form { margin-bottom: 20px; padding: 15px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>Test Books Page</h1>
    
    <!-- Search Form -->
    <div class="search-form">
        <form method="GET">
            <input type="text" name="search" placeholder="Search books..." value="<?php echo e(request('search')); ?>" style="padding: 8px; width: 300px;">
            <button type="submit" style="padding: 8px 15px;">Search</button>
            <a href="<?php echo e(url('/test-books')); ?>" style="padding: 8px 15px; background: #ccc; text-decoration: none; color: black;">Clear</a>
        </form>
    </div>

    <!-- Books Display -->
    <?php
        $query = \App\Models\Book::query();
        
        if(request('search')) {
            $search = request('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('author', 'like', "%{$search}%")
                  ->orWhere('isbn', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }
        
        $books = $query->get();
    ?>

    <p><strong>Total Books Found: <?php echo e($books->count()); ?></strong></p>
    
    <?php if(request('search')): ?>
        <p><em>Search term: "<?php echo e(request('search')); ?>"</em></p>
    <?php endif; ?>

    <?php $__empty_1 = true; $__currentLoopData = $books; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $book): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="book">
            <h3><?php echo e($book->title); ?></h3>
            <p><strong>Author:</strong> <?php echo e($book->author); ?></p>
            <p><strong>Category:</strong> <?php echo e($book->category ?? 'N/A'); ?></p>
            <p><strong>ISBN:</strong> <?php echo e($book->isbn); ?></p>
            <p><strong>Available:</strong> <?php echo e($book->available_copies); ?>/<?php echo e($book->total_copies); ?></p>
            <?php if($book->description): ?>
                <p><strong>Description:</strong> <?php echo e($book->description); ?></p>
            <?php endif; ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <p style="color: red;">No books found!</p>
    <?php endif; ?>

    <hr>
    <h2>Debug Info:</h2>
    <p><strong>Request Search:</strong> <?php echo e(request('search') ?? 'None'); ?></p>
    <p><strong>All Books Count:</strong> <?php echo e(\App\Models\Book::count()); ?></p>
    
    <h3>All Books in Database:</h3>
    <?php $__currentLoopData = \App\Models\Book::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $book): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><?php echo e($book->title); ?> by <?php echo e($book->author); ?></li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</body>
</html>
<?php /**PATH C:\Xampp\htdocs\iti_projec\resources\views/test-books.blade.php ENDPATH**/ ?>