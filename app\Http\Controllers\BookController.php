<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BorrowedBook;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookController extends Controller
{
    /**
     * Display all books for students
     */
    public function index(Request $request)
    {
        $query = Book::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('author', 'like', "%{$search}%")
                  ->orWhere('isbn', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Availability filter
        if ($request->filled('available') && $request->available == '1') {
            $query->where('available_copies', '>', 0);
        }

        $books = $query->paginate(12);
        $categories = Book::distinct()->pluck('category')->filter();

        return view('books.index', compact('books', 'categories'));
    }

    /**
     * Show book details
     */
    public function show(Book $book)
    {
        return view('books.show', compact('book'));
    }

    /**
     * Borrow a book
     */
    public function borrow(Request $request, Book $book)
    {
        $user = auth()->user();

        // Check if user is a student
        if (!$user->isStudent()) {
            return redirect()->back()->with('error', 'Only students can borrow books.');
        }

        // Check if book is available
        if (!$book->isAvailable()) {
            return redirect()->back()->with('error', 'This book is currently not available.');
        }

        // Check if user already has this book borrowed
        $existingBorrow = BorrowedBook::where('user_id', $user->id)
            ->where('book_id', $book->id)
            ->where('status', 'borrowed')
            ->first();

        if ($existingBorrow) {
            return redirect()->back()->with('error', 'You have already borrowed this book.');
        }

        // Check borrowing limit (e.g., max 5 books per student)
        $currentBorrowedCount = BorrowedBook::where('user_id', $user->id)
            ->where('status', 'borrowed')
            ->count();

        if ($currentBorrowedCount >= 5) {
            return redirect()->back()->with('error', 'You have reached the maximum borrowing limit of 5 books.');
        }

        // Create borrowing record
        $borrowedBook = BorrowedBook::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'borrowed_date' => now()->toDateString(),
            'return_date' => now()->addDays(14)->toDateString(), // 2 weeks borrowing period
            'status' => 'borrowed',
        ]);

        // Decrease available copies
        $book->borrowBook();

        return redirect()->route('student.dashboard')
            ->with('success', 'Book borrowed successfully! Please return by ' .
                   Carbon::parse($borrowedBook->return_date)->format('M d, Y'));
    }

    /**
     * Return a borrowed book
     */
    public function returnBook(BorrowedBook $borrowedBook)
    {
        $user = auth()->user();

        // Check if the borrowed book belongs to the current user
        if ($borrowedBook->user_id !== $user->id) {
            return redirect()->back()->with('error', 'You can only return your own borrowed books.');
        }

        // Check if book is already returned
        if ($borrowedBook->status === 'returned') {
            return redirect()->back()->with('error', 'This book has already been returned.');
        }

        // Mark as returned
        $borrowedBook->markAsReturned();

        return redirect()->back()->with('success', 'Book returned successfully!');
    }
}
