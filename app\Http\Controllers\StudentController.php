<?php

namespace App\Http\Controllers;

use App\Models\BorrowedBook;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class StudentController extends Controller
{
    /**
     * Display student dashboard
     */
    public function dashboard()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        $borrowedBooks = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->where('status', 'borrowed')
            ->get();

        $borrowingHistory = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->latest()
            ->take(10)
            ->get();

        $overdueBooks = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->where('status', 'borrowed')
            ->where('return_date', '<', now()->toDateString())
            ->get();

        return view('student.dashboard', compact(
            'borrowedBooks',
            'borrowingHistory',
            'overdueBooks'
        ));
    }

    /**
     * Show student profile
     */
    public function profile()
    {
        return view('student.profile');
    }

    /**
     * Update student profile
     */
    public function updateProfile(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'student_id' => ['required', 'string', Rule::unique('users')->ignore($user->id)],
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Check current password if new password is provided
        if ($request->filled('password')) {
            if (!$request->filled('current_password') || !Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'student_id' => $request->student_id,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return redirect()->route('student.profile')->with('success', 'Profile updated successfully!');
    }

    /**
     * Show borrowing history
     */
    public function borrowingHistory()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        $borrowingHistory = BorrowedBook::with('book')
            ->where('user_id', $user->id)
            ->latest()
            ->paginate(15);

        return view('student.borrowing-history', compact('borrowingHistory'));
    }
}
